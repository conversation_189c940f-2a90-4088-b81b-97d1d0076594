# Archivo: client_report_loader.py

import pandas as pd
import re

def _standardize_col_name(col_name):
    """Limpia y estandariza un nombre de columna."""
    if not isinstance(col_name, str):
        return str(col_name)
    # Convierte a minúsculas
    name = col_name.lower()
    # Reemplaza espacios y caracteres no alfanuméricos con guion bajo
    name = re.sub(r'\s+', '_', name)
    name = re.sub(r'[^a-z0-9_]', '', name)
    # Elimina guiones bajos duplicados
    name = re.sub(r'_+', '_', name)
    # Elimina guiones bajos al principio o al final
    name = name.strip('_')
    return name

def _find_header_row(file_path, keyword, max_rows_to_scan=20):
    """
    Escanea las primeras filas de un Excel para encontrar la fila del encabezado.
    Devuelve el número de la fila (basado en 0).
    """
    try:
        df_preview = pd.read_excel(file_path, header=None, nrows=max_rows_to_scan)
        for index, row in df_preview.iterrows():
            if any(keyword in str(cell) for cell in row):
                return index
    except Exception:
        return None
    return None

def cargar_reporte_descargos(ruta_archivo, log_callback):
    """
    Carga el reporte de descargos del cliente desde un archivo .xlsx.
    Encuentra dinámicamente el encabezado, limpia los datos y estandariza las columnas.
    """
    log_callback("Iniciando carga del reporte de descargos del cliente...")
    
    # 1. Encontrar la fila del encabezado dinámicamente
    header_keyword = 'Pedimento Impo'
    log_callback(f"Buscando encabezado con la palabra clave: '{header_keyword}'...")
    header_row_index = _find_header_row(ruta_archivo, header_keyword)

    if header_row_index is None:
        log_callback(f"❌ ERROR: No se pudo encontrar la fila del encabezado que contenga '{header_keyword}'.")
        log_callback("Por favor, verifica que el archivo del cliente sea correcto.")
        return None
        
    log_callback(f"✅ Encabezado encontrado en la fila {header_row_index + 1}.")

    # 2. Cargar el archivo completo usando el encabezado correcto
    try:
        df = pd.read_excel(ruta_archivo, header=header_row_index)
        log_callback(f"Archivo Excel cargado. {len(df)} filas y {len(df.columns)} columnas encontradas.")
    except Exception as e:
        log_callback(f"❌ ERROR: No se pudo leer el archivo Excel. Causa: {e}")
        return None

    # 3. Limpieza de datos
    # Eliminar filas y columnas que estén completamente vacías
    df.dropna(how='all', axis=0, inplace=True)
    df.dropna(how='all', axis=1, inplace=True)

    # Estandarizar nombres de columnas
    original_columns = df.columns.tolist()
    df.columns = [_standardize_col_name(col) for col in original_columns]
    standardized_columns = df.columns.tolist()
    log_callback("Columnas estandarizadas para un uso más fácil en el código.")
    # log_callback(f"Ejemplo: '{original_columns[2]}' -> '{standardized_columns[2]}'")

    # 4. Verificación de columnas esenciales
    required_cols = [
        'pedimento_impo', 'secuencia_impo', 'cantidad_descargada', 'factorincorporacion', 'tasa_igi'
    ]
    missing_cols = [col for col in required_cols if col not in df.columns]

    if missing_cols:
        log_callback(f"❌ ERROR: Faltan columnas esenciales en el reporte del cliente después de la estandarización.")
        log_callback(f"Columnas faltantes: {', '.join(missing_cols)}")
        return None
    
    log_callback("✅ Verificación de columnas esenciales completada.")
    log_callback(f"Reporte del cliente cargado y preparado exitosamente con {len(df)} registros.")
    
    return df