# Archivo: main_app.py

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
import threading
import webbrowser
import os
import pandas as pd

# Importamos todos nuestros módulos personalizados
import data_stage_loader 
import client_report_loader
import processing_engine
import output_generator

class DrawbackApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Automatización de Reportes de Drawback v1.0")
        self.root.geometry("850x650")
        self.root.configure(bg='#f0f0f0')

        self.ds_folder_path = tk.StringVar()
        self.client_report_path = tk.StringVar()

        main_frame = tk.Frame(root, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        title_label = tk.Label(main_frame, text="⚙️ Herramienta de Automatización de Drawback", font=('Segoe UI', 16, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=(0, 20))

        # --- Frame para Selección de Inputs ---
        inputs_frame = tk.LabelFrame(main_frame, text="📁 1. Selección de Archivos de Entrada", font=('Segoe UI', 10, 'bold'), bg='#ffffff', fg='#34495e', padx=15, pady=15)
        inputs_frame.pack(fill=tk.X, pady=(0, 15))

        # Selector para Carpeta de Data Stage
        tk.Label(inputs_frame, text="Carpeta del Data Stage:", font=('Segoe UI', 10), bg='white').grid(row=0, column=0, sticky='w', pady=2)
        ds_entry = tk.Entry(inputs_frame, textvariable=self.ds_folder_path, state='readonly', width=70)
        ds_entry.grid(row=1, column=0, padx=(0, 10))
        tk.Button(inputs_frame, text="Buscar...", command=self.select_ds_folder).grid(row=1, column=1)

        # Selector para Reporte de Cliente
        tk.Label(inputs_frame, text="Reporte de Descargos del Cliente (.xlsx):", font=('Segoe UI', 10), bg='white').grid(row=2, column=0, sticky='w', pady=(10, 2))
        client_entry = tk.Entry(inputs_frame, textvariable=self.client_report_path, state='readonly', width=70)
        client_entry.grid(row=3, column=0, padx=(0, 10))
        tk.Button(inputs_frame, text="Buscar...", command=self.select_client_report).grid(row=3, column=1)

        # --- Frame de Ejecución ---
        action_frame = tk.LabelFrame(main_frame, text="🚀 2. Ejecutar Proceso", font=('Segoe UI', 10, 'bold'), bg='#ffffff', fg='#34495e', padx=15, pady=15)
        action_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.start_button = tk.Button(action_frame, text="Generar Reportes de Drawback", command=self.start_drawback_thread, bg='#27ae60', fg='white', font=('Segoe UI', 12, 'bold'), relief='flat', padx=30, pady=12)
        self.start_button.pack(pady=10)

        # --- Frame de Log ---
        log_frame = tk.LabelFrame(main_frame, text="📋 Registro de Progreso", font=('Segoe UI', 10, 'bold'), bg='#ffffff', fg='#34495e', padx=15, pady=15)
        log_frame.pack(fill=tk.BOTH, expand=True)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, state='disabled', height=15, font=('Consolas', 9), bg='#2c3e50', fg='#ecf0f1')
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=5)

    def log(self, message):
        """Función para escribir en el log de la UI de forma segura desde threads."""
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')
        self.root.update_idletasks()

    def select_ds_folder(self):
        path = filedialog.askdirectory()
        if path:
            self.ds_folder_path.set(path)
            self.log(f"Carpeta Data Stage seleccionada: {path}")

    def select_client_report(self):
        path = filedialog.askopenfilename(filetypes=[("Excel Files", "*.xlsx"), ("All files", "*.*")])
        if path:
            self.client_report_path.set(path)
            self.log(f"Reporte de Cliente seleccionado: {path}")

    def start_drawback_thread(self):
        """Inicia el proceso principal en un hilo separado para no congelar la UI."""
        if not self.ds_folder_path.get() or not self.client_report_path.get():
            messagebox.showerror("Error", "Por favor, selecciona la carpeta del Data Stage Y el reporte del cliente.")
            return

        self.start_button.config(state='disabled', text="Procesando...")
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')
        
        thread = threading.Thread(target=self.run_drawback_process)
        thread.daemon = True
        thread.start()

    def run_drawback_process(self):
        """
        Esta es la función principal que orquesta todo el flujo de trabajo.
        Se ejecuta en un hilo secundario.
        """
        try:
            self.log("========================================")
            self.log("INICIANDO PROCESO DE GENERACIÓN DE DRAWBACK")
            self.log("========================================")
            
            # Determinar la carpeta de salida (donde está el reporte del cliente)
            output_folder = os.path.dirname(self.client_report_path.get())
            self.log(f"Carpeta de salida para reportes: {output_folder}")

            # --- FASE 1: Carga del Data Stage ---
            ds_dfs = data_stage_loader.cargar_data_stage(self.ds_folder_path.get(), self.log)
            if not ds_dfs:
                self.log("❌ ERROR: No se pudieron cargar los datos del Data Stage. Proceso cancelado.")
                messagebox.showerror("Error", "No se pudieron cargar los datos del Data Stage. Revisa la carpeta y el log.")
                self.start_button.config(state='normal', text="Generar Reportes de Drawback")
                return

            # --- FASE 2: Carga del Reporte del Cliente ---
            self.log("\n--- Cargando Reporte del Cliente ---")
            client_df = client_report_loader.cargar_reporte_descargos(self.client_report_path.get(), self.log)
            if client_df is None:
                self.log("❌ ERROR: No se pudo cargar el reporte del cliente. Proceso cancelado.")
                messagebox.showerror("Error", "No se pudo cargar el reporte del cliente. Revisa el archivo y el log.")
                self.start_button.config(state='normal', text="Generar Reportes de Drawback")
                return
            
            # --- FASE 3: Motor de Procesamiento ---
            self.log("\n--- Procesando y Calculando Drawback ---")
            resultados_mismo_estado, resultados_transformado = processing_engine.procesar_drawback(ds_dfs, client_df, self.log)

            if not resultados_mismo_estado and not resultados_transformado:
                 self.log("⚠️ AVISO: El procesamiento finalizó pero no se generaron registros de drawback válidos.")
            else:
                 self.log(f"Procesamiento finalizado. Resultados: {len(resultados_mismo_estado)} de Mismo Estado, {len(resultados_transformado)} de Transformado.")
            
            # --- FASE 4: Generación de Archivos de Salida ---
            self.log("\n--- Generando Archivos de Salida ---")
            
            # Convertimos las listas de resultados en DataFrames
            df_mismo_estado = pd.DataFrame(resultados_mismo_estado)
            df_transformado = pd.DataFrame(resultados_transformado)
            
            output_files = output_generator.generar_archivos_excel(
                df_mismo_estado, 
                df_transformado, 
                output_folder, 
                self.log
            )
            
            self.log("\n========================================")
            self.log("🎉 PROCESO COMPLETADO EXITOSAMENTE")
            self.log("========================================")

            if output_files:
                msg = "¡Proceso finalizado!\n\nSe generaron los siguientes archivos:\n"
                for f in output_files:
                    msg += f"• {os.path.basename(f)}\n"
                msg += f"\nUbicación: {output_folder}"
                
                if messagebox.askyesno("Éxito", msg + "\n\n¿Deseas abrir la carpeta de salida?"):
                    webbrowser.open(output_folder)
            else:
                messagebox.showinfo("Proceso Finalizado", "El proceso terminó, pero no se generó ningún archivo de drawback porque no se encontraron datos válidos para procesar.")

        except Exception as e:
            self.log(f"❌ ERROR FATAL: {e}")
            messagebox.showerror("Error Fatal", f"Ocurrió un error inesperado:\n\n{e}")
        finally:
            self.start_button.config(state='normal', text="Generar Reportes de Drawback")

if __name__ == "__main__":
    root = tk.Tk()
    app = DrawbackApp(root)
    root.mainloop()