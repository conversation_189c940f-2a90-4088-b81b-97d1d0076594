# Archivo: output_generator.py

import os
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter

# --- DEFINICIONES DE ESTILOS Y COLORES ---
ESTILOS = {
    'header_general': {
        'fill': PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid'),  # Gris oscuro
        'font': Font(color='FFFFFF', bold=True, size=11),
        'alignment': Alignment(horizontal='center', vertical='center', wrap_text=True)
    },
    'header_importacion': {
        'fill': PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid'),  # Azul
        'font': Font(color='FFFFFF', bold=True, size=11),
        'alignment': Alignment(horizontal='center', vertical='center', wrap_text=True)
    },
    'header_exportacion': {
        'fill': PatternFill(start_color='70AD47', end_color='70AD47', fill_type='solid'),  # Verde
        'font': Font(color='FFFFFF', bold=True, size=11),
        'alignment': Alignment(horizontal='center', vertical='center', wrap_text=True)
    },
    'header_devolucion': {
        'fill': PatternFill(start_color='E74C3C', end_color='E74C3C', fill_type='solid'),  # Rojo
        'font': Font(color='FFFFFF', bold=True, size=11),
        'alignment': Alignment(horizontal='center', vertical='center', wrap_text=True)
    },
    'header_verificacion': {
        'fill': PatternFill(start_color='F39C12', end_color='F39C12', fill_type='solid'),  # Naranja
        'font': Font(color='FFFFFF', bold=True, size=11),
        'alignment': Alignment(horizontal='center', vertical='center', wrap_text=True)
    },
    'data_cell': {
        'font': Font(size=10),
        'alignment': Alignment(horizontal='left', vertical='center'),
        'border': Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    },
    'numeric_cell': {
        'font': Font(size=10),
        'alignment': Alignment(horizontal='right', vertical='center'),
        'border': Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    }
}

# Mapeo de secciones a estilos
SECTION_STYLES = {
    'general': 'header_general',
    'importacion': 'header_importacion',
    'exportacion': 'header_exportacion',
    'devolucion': 'header_devolucion',
    'verificacion': 'header_verificacion'
}

# --- ORDEN EXACTO DE COLUMNAS SEGÚN ESPECIFICACIÓN ---

# Orden para MISMO ESTADO (sin Factor de incorporación)
COLUMNS_MISMO_ESTADO = [
    # Sección A: Datos generales
    'RFC del solicitante',
    'Año',
    'Folio',

    # Sección B: Importación
    'Número del pedimento (Importación)',
    'Pedimento rectificado (Importación)',
    'Secuencia / Partida del pedimento (Importación)',
    'Fracción arancelaria (Importación)',
    'Descripción de la mercancía (Importación)',
    'País de origen',
    'Medidas / Tamaño / Modelo (Importación)',
    'Cantidad solicitada (Importación)',
    'Unidad de Medida Comercial (Importación)',
    'Precio unitario (Importación)',
    'Número de factura (Importación)',

    # Sección C: Exportación
    'Número del pedimento (Exportación)',
    'Fecha (Exportación)',
    'Pedimento rectificado (Exportación)',
    'Secuencia / Partida del pedimento (Exportación)',
    'Fracción arancelaria (Exportación)',
    'Descripción de la mercancía (Exportación)',
    'País de destino (Exportación)',
    'Medidas / Tamaño / Modelo (Exportación)',
    'Cantidad solicitada (Exportación)',
    'Unidad de Medida Comercial (Exportación)',
    'Precio unitario (Exportación)',
    'Número de factura (Exportación)',

    # Sección D: Devolución
    'Monto a devolver en moneda nacional'
]

# Orden para TRANSFORMADO (incluye Factor de incorporación)
COLUMNS_TRANSFORMADO = [
    # Sección A: Datos generales
    'RFC del solicitante',
    'Año',
    'Folio',
    'Factor de incorporación',

    # Sección B: Importación
    'Número del pedimento (Importación)',
    'Pedimento rectificado (Importación)',
    'Secuencia / Partida del pedimento (Importación)',
    'Fracción arancelaria (Importación)',
    'Descripción de la mercancía (Importación)',
    'País de origen',
    'Medidas / Tamaño / Modelo (Importación)',
    'Cantidad solicitada (Importación)',
    'Unidad de Medida Comercial (Importación)',
    'Precio unitario (Importación)',
    'Número de factura (Importación)',

    # Sección C: Exportación
    'Número del pedimento (Exportación)',
    'Fecha (Exportación)',
    'Pedimento rectificado (Exportación)',
    'Secuencia / Partida del pedimento (Exportación)',
    'Fracción arancelaria (Exportación)',
    'Descripción de la mercancía (Exportación)',
    'País de destino (Exportación)',
    'Medidas / Tamaño / Modelo (Exportación)',
    'Cantidad solicitada (Exportación)',
    'Unidad de Medida Comercial (Exportación)',
    'Precio unitario (Exportación)',
    'Número de factura (Exportación)',

    # Sección D: Devolución
    'Monto a devolver en moneda nacional'
]

# Definición de secciones para aplicar colores
COLUMN_SECTIONS = {
    'general': ['RFC del solicitante', 'Año', 'Folio', 'Factor de incorporación'],
    'importacion': [
        'Número del pedimento (Importación)',
        'Pedimento rectificado (Importación)',
        'Secuencia / Partida del pedimento (Importación)',
        'Fracción arancelaria (Importación)',
        'Descripción de la mercancía (Importación)',
        'País de origen',
        'Medidas / Tamaño / Modelo (Importación)',
        'Cantidad solicitada (Importación)',
        'Unidad de Medida Comercial (Importación)',
        'Precio unitario (Importación)',
        'Número de factura (Importación)'
    ],
    'exportacion': [
        'Número del pedimento (Exportación)',
        'Fecha (Exportación)',
        'Pedimento rectificado (Exportación)',
        'Secuencia / Partida del pedimento (Exportación)',
        'Fracción arancelaria (Exportación)',
        'Descripción de la mercancía (Exportación)',
        'País de destino (Exportación)',
        'Medidas / Tamaño / Modelo (Exportación)',
        'Cantidad solicitada (Exportación)',
        'Unidad de Medida Comercial (Exportación)',
        'Precio unitario (Exportación)',
        'Número de factura (Exportación)'
    ],
    'devolucion': ['Monto a devolver en moneda nacional']
}


def _aplicar_formato_profesional(ws, df, column_order, log_callback):
    """Aplica formato profesional con colores por sección al worksheet."""

    # Configurar altura de filas
    ws.row_dimensions[1].height = 30  # Header más alto

    # Aplicar estilos a headers por sección
    for section, columns in COLUMN_SECTIONS.items():
        style_name = SECTION_STYLES.get(section, 'header_general')
        style = ESTILOS[style_name]

        for col_name in columns:
            if col_name in column_order:
                # Encontrar la posición de la columna
                col_idx = column_order.index(col_name) + 1
                cell = ws.cell(row=1, column=col_idx)

                # Aplicar estilo
                cell.fill = style['fill']
                cell.font = style['font']
                cell.alignment = style['alignment']

                # Ajustar ancho de columna
                col_letter = get_column_letter(col_idx)
                if 'Descripción' in col_name:
                    ws.column_dimensions[col_letter].width = 40
                elif 'Número del pedimento' in col_name:
                    ws.column_dimensions[col_letter].width = 18
                elif 'Monto' in col_name or 'Precio' in col_name:
                    ws.column_dimensions[col_letter].width = 15
                else:
                    ws.column_dimensions[col_letter].width = 12

    # Aplicar estilos a celdas de datos
    for row_idx in range(2, len(df) + 2):
        for col_idx in range(1, len(column_order) + 1):
            cell = ws.cell(row=row_idx, column=col_idx)

            # Determinar si es numérico
            col_name = column_order[col_idx - 1]
            if 'Monto' in col_name or 'Precio' in col_name or 'Cantidad' in col_name:
                # Aplicar formato numérico
                cell.font = ESTILOS['numeric_cell']['font']
                cell.alignment = ESTILOS['numeric_cell']['alignment']
                cell.border = ESTILOS['numeric_cell']['border']

                # Formato de número si es numérico
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0.00'
            else:
                # Aplicar formato de texto
                cell.font = ESTILOS['data_cell']['font']
                cell.alignment = ESTILOS['data_cell']['alignment']
                cell.border = ESTILOS['data_cell']['border']

    # Congelar primera fila
    ws.freeze_panes = 'A2'

    log_callback("✅ Formato profesional aplicado al Excel")

def _escribir_excel_profesional(df, base_filename, column_order, output_folder, log_callback):
    """Función mejorada para escribir un DataFrame a un archivo Excel con formato profesional."""
    if df.empty:
        log_callback(f"⚠️ Aviso: No hay datos para generar el archivo '{base_filename}'. Se omitirá.")
        return None

    # Reordenar y seleccionar las columnas finales
    try:
        df_final = df[column_order]
    except KeyError as e:
        log_callback(f"❌ ERROR: Falta una columna esperada en los datos procesados: {e}")
        log_callback(f"   Columnas disponibles: {list(df.columns)}")
        log_callback(f"   Columnas esperadas: {column_order}")
        return None

    # Crear nombre de archivo con fecha
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    final_filename = f"{timestamp}_{base_filename}.xlsx"
    output_path = os.path.join(output_folder, final_filename)

    try:
        # Crear workbook y worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = base_filename.replace('DRAWBACK_', '')

        # Escribir datos
        for r in dataframe_to_rows(df_final, index=False, header=True):
            ws.append(r)

        # Aplicar formato profesional
        _aplicar_formato_profesional(ws, df_final, column_order, log_callback)

        # Guardar archivo
        wb.save(output_path)
        log_callback(f"✅ Archivo Excel profesional generado: {final_filename}")
        return output_path

    except Exception as e:
        log_callback(f"❌ ERROR: No se pudo escribir el archivo Excel en '{output_path}'. Causa: {e}")
        return None



def generar_archivos_excel(df_mismo_estado, df_transformado, output_folder, log_callback):
    """
    Toma los DataFrames procesados y genera los dos archivos Excel de salida con formato profesional.
    """
    log_callback("Iniciando generación de archivos Excel profesionales...")

    created_files = []

    # Generar archivo para "Mismo Estado" con formato profesional
    path1 = _escribir_excel_profesional(
        df_mismo_estado,
        "DRAWBACK_MISMO_ESTADO",
        COLUMNS_MISMO_ESTADO,
        output_folder,
        log_callback
    )
    if path1:
        created_files.append(path1)

    # Generar archivo para "Transformado" con formato profesional
    path2 = _escribir_excel_profesional(
        df_transformado,
        "DRAWBACK_TRANSFORMADO",
        COLUMNS_TRANSFORMADO,
        output_folder,
        log_callback
    )
    if path2:
        created_files.append(path2)

    log_callback("✅ Generación de archivos Excel profesionales finalizada.")
    return created_files