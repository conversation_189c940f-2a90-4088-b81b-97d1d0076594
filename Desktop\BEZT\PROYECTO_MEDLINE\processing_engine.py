# Archivo: processing_engine.py

import pandas as pd

def _descomponer_pedimento_cliente(pedimento_completo):
    """
    Descompone un pedimento del cliente en sus partes para buscar en el Data Stage.

    Formato del cliente: AASSPPPPPPPPPPPP
    - AA: año (2 dígitos)
    - SS: sección aduanera (2 dígitos)
    - PPPP: patente (4 dígitos típicamente)
    - PPPPPPPP: pedimento base (resto)

    Args:
        pedimento_completo: Pedimento completo del cliente (ej: 252414885011472)

    Returns:
        dict con 'año', 'seccion', 'patente', 'pedimento_base' o None si error
    """
    try:
        pedimento_str = str(pedimento_completo).strip()

        if len(pedimento_str) < 8:
            return None

        # Extraer componentes
        año = pedimento_str[:2]  # Primeros 2 dígitos
        seccion = pedimento_str[2:4]  # Siguientes 2 dígitos

        # Para patente, necesitamos determinar cuántos dígitos tomar
        # Típicamente son 4 dígitos, pero puede variar
        # Vamos a asumir 4 dígitos por ahora
        patente = pedimento_str[4:8]  # Siguientes 4 dígitos
        pedimento_base = pedimento_str[8:]  # El resto

        # Reconstruir sección completa (agregar 0 si es necesario)
        seccion_completa = seccion + "0" if len(seccion) == 2 else seccion

        return {
            'año': f"20{año}",  # Convertir a año completo
            'seccion': seccion_completa,
            'patente': patente,
            'pedimento_base': pedimento_base
        }

    except Exception:
        return None

def _get_safe_value(row, column_name, default_value=''):
    """
    Obtiene un valor de una fila de forma segura, devolviendo un valor por defecto
    si la columna no existe o el valor es nulo.
    """
    try:
        if column_name in row.index:
            value = row[column_name]
            return value if pd.notna(value) else default_value
        else:
            return default_value
    except Exception:
        return default_value

def _get_final_pedimento(patente, pedimento, aduana, df_701):
    """
    Busca un pedimento en la tabla 701 para encontrar su versión final si fue rectificado.
    Devuelve la tupla (patente, pedimento, aduana) final.
    """
    # Convertir a string para asegurar la comparación
    pedimento = str(pedimento)
    patente = str(patente)
    aduana = str(aduana)

    # Filtrar por el pedimento original/anterior
    rect_row = df_701[
        (df_701['pedimentoanterior'] == pedimento) & 
        (df_701['patenteanterior'] == patente) &
        (df_701['seccionaduaneraanterior'] == aduana)
    ]
    
    if not rect_row.empty:
        # Se encontró una rectificación, ahora buscamos si esta rectificación fue rectificada a su vez (recursividad)
        new_patente = rect_row.iloc[0]['patente']
        new_pedimento = rect_row.iloc[0]['pedimento']
        new_aduana = rect_row.iloc[0]['seccionaduanera']
        # Llamada recursiva para encontrar la última versión
        return _get_final_pedimento(new_patente, new_pedimento, new_aduana, df_701)
    else:
        # No se encontraron más rectificaciones, esta es la versión final
        return patente, pedimento, aduana

def procesar_drawback(ds_dfs, client_df, log_callback):
    """
    El motor principal del sistema. Procesa cada fila del reporte del cliente,
    la valida contra el Data Stage, calcula el drawback y la clasifica.
    """
    log_callback("Iniciando motor de procesamiento...")

    # Extraer los DataFrames necesarios del diccionario del Data Stage
    df_551 = ds_dfs.get('551')
    df_701 = ds_dfs.get('701')

    if df_551 is None or df_701 is None:
        log_callback("❌ ERROR: Faltan tablas esenciales (551 o 701) en el Data Stage.")
        return [], []

    resultados_mismo_estado = []
    resultados_transformado = []
    
    total_rows = len(client_df)
    log_callback(f"Procesando {total_rows} registros del reporte del cliente...")

    # --- Bucle principal: iterar sobre cada descarga del cliente ---
    for index, row in client_df.iterrows():
        numeroparte = _get_safe_value(row, 'numeroparte', f'registro_{index + 1}')
        log_callback(f"\n--- Procesando registro {index + 1}/{total_rows} (Parte: {numeroparte}) ---")

        try:
            # 1. VALIDACIÓN DE RECTIFICACIÓN (Pedimento de Importación)
            patente_impo_orig = _get_safe_value(row, 'patente_impo')
            pedimento_impo_orig = _get_safe_value(row, 'pedimento_impo')
            aduana_impo_orig = _get_safe_value(row, 'aduana_impo')

            # Verificar que tenemos los datos mínimos necesarios
            if not patente_impo_orig or not pedimento_impo_orig or not aduana_impo_orig:
                log_callback(f"❌ ERROR: Faltan datos básicos de importación (patente, pedimento o aduana). Se omite este registro.")
                continue

            # Descomponer el pedimento del cliente para obtener los componentes base
            componentes_orig = _descomponer_pedimento_cliente(pedimento_impo_orig)
            if componentes_orig is None:
                log_callback(f"❌ ERROR: No se pudo descomponer el pedimento del cliente: {pedimento_impo_orig}")
                continue

            # Usar los componentes base para verificar rectificaciones
            final_patente, final_pedimento, final_aduana = _get_final_pedimento(
                componentes_orig['patente'], componentes_orig['pedimento_base'], componentes_orig['seccion'], df_701
            )

            # Verificar si hubo rectificación comparando los componentes base
            pedimento_rectificado = final_pedimento != componentes_orig['pedimento_base']

            if pedimento_rectificado:
                log_callback(f"⚠️ Pedimento de importación rectificado. Original: {pedimento_impo_orig}, Final: {final_pedimento}")
            else:
                # Si no hay rectificación, usar los datos originales
                final_patente = componentes_orig['patente']
                final_pedimento = componentes_orig['pedimento_base']
                final_aduana = componentes_orig['seccion']
            
            # 2. ENCONTRAR LA PARTIDA CORRESPONDIENTE EN EL DATA STAGE (Tabla 551)
            secuencia_impo = str(_get_safe_value(row, 'secuencia_impo'))

            if not secuencia_impo or secuencia_impo == '':
                log_callback(f"❌ ERROR: Falta la secuencia de importación. Se omite este registro.")
                continue

            # Usar los componentes ya descompuestos para buscar en Data Stage
            log_callback(f"🔍 Usando pedimento {pedimento_impo_orig} (base: {final_pedimento}):")
            log_callback(f"   → Año: {componentes_orig['año']}, Sección: {final_aduana}, Patente: {final_patente}, Pedimento base: {final_pedimento}")

            # Buscar en Data Stage usando los componentes finales (después de rectificación si aplica)
            partida_ds = df_551[
                (df_551['patente'] == final_patente) &
                (df_551['pedimento'] == final_pedimento) &
                (df_551['seccionaduanera'] == final_aduana) &
                (df_551['secuenciafraccion'] == secuencia_impo)
            ]

            if partida_ds.empty:
                # Fallback: búsqueda directa para compatibilidad
                log_callback(f"⚠️ No encontrado con pedimento formado. Intentando búsqueda directa...")
                partida_ds = df_551[
                    (df_551['patente'] == final_patente) &
                    (df_551['pedimento'] == final_pedimento) &
                    (df_551['seccionaduanera'] == final_aduana) &
                    (df_551['secuenciafraccion'] == secuencia_impo)
                ]

                if not partida_ds.empty:
                    log_callback(f"✅ Encontrado con búsqueda directa")
                else:
                    log_callback(f"❌ ERROR: No se encontró la partida en el Data Stage.")
                    log_callback(f"   Buscando: Patente={final_patente}, Pedimento={final_pedimento}, Sección={final_aduana}, Secuencia={secuencia_impo}")
                    log_callback(f"   Se omite este registro.")
                    continue
            else:
                log_callback(f"✅ Partida encontrada usando pedimento descompuesto")
            if len(partida_ds) > 1:
                log_callback(f"❌ ERROR: Se encontraron múltiples ({len(partida_ds)}) partidas coincidentes. Se omite este registro.")
                continue

            partida_ds_row = partida_ds.iloc[0]

            # 3. EXTRACCIÓN Y CÁLCULO DEL DRAWBACK
            # Convertir a numérico, manejando errores
            valor_aduana_total_ds = pd.to_numeric(_get_safe_value(partida_ds_row, 'valoraduana', 0), errors='coerce')
            cantidad_total_ds = pd.to_numeric(_get_safe_value(partida_ds_row, 'cantidadumtarifa', 0), errors='coerce')
            tasa_igi_cliente = pd.to_numeric(_get_safe_value(row, 'tasa_igi', 0), errors='coerce')
            cantidad_descargada_cliente = pd.to_numeric(_get_safe_value(row, 'cantidad_descargada', 0), errors='coerce')

            if pd.isna(valor_aduana_total_ds) or pd.isna(cantidad_total_ds) or pd.isna(tasa_igi_cliente) or pd.isna(cantidad_descargada_cliente):
                log_callback("❌ ERROR: Datos numéricos inválidos en el Data Stage o reporte del cliente. Se omite este registro.")
                continue
            
            if cantidad_total_ds == 0:
                log_callback("❌ ERROR: La cantidad total en la partida del Data Stage es 0. No se puede calcular el costo unitario.")
                continue

            # El cálculo proporcional que definimos
            costo_unitario_aduana = valor_aduana_total_ds / cantidad_total_ds
            valor_aduana_proporcional = costo_unitario_aduana * cantidad_descargada_cliente
            monto_drawback_calculado = valor_aduana_proporcional * (tasa_igi_cliente / 100)

            log_callback(f"Cálculo de Drawback: ${monto_drawback_calculado:.4f}")

            # 4. CONSTRUIR LA FILA DE SALIDA con el formato del SAT
            # Obtener fecha de exportación de forma segura para el año
            fechapago_expo = _get_safe_value(row, 'fechapago_expo')
            try:
                año_expo = pd.to_datetime(fechapago_expo).year if fechapago_expo else ''
            except:
                año_expo = ''

            # Verificar si el pedimento de exportación fue rectificado
            pedim_orig_expo = _get_safe_value(row, 'pedim_orig_expo')
            pedimento_expo_rectificado = "si" if pedim_orig_expo and str(pedim_orig_expo) != '0' and str(pedim_orig_expo).strip() != '' else "no"

            output_row = {
                # Sección A: Datos generales
                'RFC del solicitante': "PME880419NU5", # Valor fijo de ejemplo
                'Año': año_expo,
                'Folio': '', # Se puede generar un folio si es necesario
                'Factor de incorporación': _get_safe_value(row, 'factorincorporacion'),

                # Sección B: Importación
                'Número del pedimento (Importación)': pedimento_impo_orig,  # Mostrar el pedimento original del cliente
                'Pedimento rectificado (Importación)': "si" if pedimento_rectificado else "no",
                'Secuencia / Partida del pedimento (Importación)': secuencia_impo,
                'Fracción arancelaria (Importación)': _get_safe_value(partida_ds_row, 'fraccion'),
                'Descripción de la mercancía (Importación)': _get_safe_value(partida_ds_row, 'descripcionmercancia'),
                'País de origen': _get_safe_value(partida_ds_row, 'paisorigendestino'),
                'Medidas / Tamaño / Modelo (Importación)': _get_safe_value(partida_ds_row, 'modelomercanciaproducto'),
                'Cantidad solicitada (Importación)': cantidad_descargada_cliente,
                'Unidad de Medida Comercial (Importación)': _get_safe_value(partida_ds_row, 'unidadmedidacomercial'),
                'Precio unitario (Importación)': costo_unitario_aduana,
                'Número de factura (Importación)': _get_safe_value(row, 'factura_impo'),

                # Sección C: Exportación
                'Número del pedimento (Exportación)': _get_safe_value(row, 'pedimento_expo'),
                'Fecha (Exportación)': fechapago_expo,
                'Pedimento rectificado (Exportación)': pedimento_expo_rectificado,
                'Secuencia / Partida del pedimento (Exportación)': _get_safe_value(row, 'secuencia_expo'),
                'Fracción arancelaria (Exportación)': _get_safe_value(row, 'fraccion_expo'),
                'Descripción de la mercancía (Exportación)': _get_safe_value(row, 'descrip_expo'),
                'País de destino (Exportación)': _get_safe_value(row, 'pais_destino'),
                'Medidas / Tamaño / Modelo (Exportación)': _get_safe_value(row, 'modelo'),
                'Cantidad solicitada (Exportación)': cantidad_descargada_cliente,
                'Unidad de Medida Comercial (Exportación)': _get_safe_value(row, 'umc_expo'),
                'Precio unitario (Exportación)': _get_safe_value(row, 'precio_unit_expo'),
                'Número de factura (Exportación)': _get_safe_value(row, 'factura_expo'),

                # Sección D: Devolución
                'Monto a devolver en moneda nacional': monto_drawback_calculado,
            }

            # 5. CLASIFICAR Y AÑADIR A LA LISTA CORRESPONDIENTE
            # La clasificación debe basarse en el campo "Ident Descarga", no solo en el factor
            ident_descarga = _get_safe_value(row, 'ident_descarga', '').lower()
            factor = pd.to_numeric(_get_safe_value(row, 'factorincorporacion', 0), errors='coerce')

            log_callback(f"Datos para clasificación: Ident='{ident_descarga}', Factor={factor}")

            # Clasificación basada en el identificador de descarga
            if "mismo estado" in ident_descarga:
                resultados_mismo_estado.append(output_row)
                log_callback("Clasificado como: MISMO ESTADO (por identificador de descarga)")
            elif "transformad" in ident_descarga or "alterna" in ident_descarga:
                resultados_transformado.append(output_row)
                log_callback("Clasificado como: TRANSFORMADO (por identificador de descarga)")
            else:
                # Fallback: usar factor de incorporación si no hay identificador claro
                if pd.notna(factor) and factor > 0:
                    resultados_transformado.append(output_row)
                    log_callback("Clasificado como: TRANSFORMADO (por factor de incorporación)")
                else:
                    resultados_mismo_estado.append(output_row)
                    log_callback("Clasificado como: MISMO ESTADO (por factor de incorporación)")

        except Exception as e:
            log_callback(f"❌ ERROR INESPERADO en registro {index + 1}: {e}. Se omite este registro.")
            continue
            
    log_callback("\n✅ Motor de procesamiento finalizado.")
    return resultados_mismo_estado, resultados_transformado